import Header from "@/components/header";
import Search from "@/components/search";

const Banner = () => {
  return (
    <div className="relative h-[75vh] w-full overflow-hidden">
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `linear-gradient(to bottom right, rgba(0,0,0,0.45), rgba(0,0,0,0.45)), url(/banner.png)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="p-10">
          <Header />
        </div>
        <div className="absolute bottom-20 left-0 right-0">
          <Search />
        </div>
      </div>
    </div>
  );
};

export default Banner;
