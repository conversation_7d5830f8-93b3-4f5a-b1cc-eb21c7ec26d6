export type SearchData = {
  location: string;
  checkIn: string;
  checkOut: string;
};

export type ServiceTabsProps = {
  activeTab: string;
  onTabChange: (_tab: string) => void;
};

export type FiltersSidebarProps = {
  filters: unknown;
  onFilterChange: (_filters: unknown) => void;
};

export type SearchHeaderProps = {
  location: string;
  checkIn: string;
  checkOut: string;
  onSearch: (_searchData: SearchData) => void;
};

export type HotelCardProps = {
  hotel: IProperty;
};

export enum PremisesTypeEnum {
  AIRSIDE = "Airside",
  LANDSIDE = "Landside",
}

export type IProperty = {
  serviceType: IServiceType;
  name: string;
  description: string;
  premisesType: PremisesTypeEnum;
  code: string;
  website: string;
  address: IAddress;
  buildYear: Date;
  acceptBookingFrom: Date;
  logoUrl: string;
  basePrice: number;
};

export type IServiceType = {
  _id: string;
  name: string;
  value: string;
};

export type IAddress = {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
};
