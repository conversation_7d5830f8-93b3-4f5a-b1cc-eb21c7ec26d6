import React, { useState } from "react";
import {
  Clock,
  CreditCard,
  Luggage,
  MapPin,
  Plane,
  Shield,
} from "lucide-react";
import { useSearchParams } from "react-router";
import type {
  FiltersSidebarProps,
  HotelCardProps,
  SearchHeaderProps,
  ServiceTabsProps,
} from "@/pages/search/types.ts";

// Search Header Component
const SearchHeader = ({
  location,
  checkIn,
  checkOut,
  onSearch,
}: SearchHeaderProps) => {
  const [searchData, setSearchData] = useState({
    location: location || "KEMPEGOWDA AIRPORT",
    checkIn: checkIn || "11:00 AM",
    checkOut: checkOut || "04:00 PM",
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2 flex-1 min-w-64">
          <Plane className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Location
            </label>
            <select
              className="mt-1 text-lg font-semibold border-none outline-none bg-transparent"
              value={searchData.location}
              onChange={(e) =>
                setSearchData({ ...searchData, location: e.target.value })
              }
            >
              <option>KEMPEGOWDA AIRPORT</option>
              <option>MUMBAI AIRPORT</option>
              <option>DELHI AIRPORT</option>
            </select>
            <p className="text-sm text-gray-500">Bengaluru, India</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Check-in
            </label>
            <input
              type="time"
              className="mt-1 text-lg font-semibold border-none outline-none"
              value={searchData.checkIn}
              onChange={(e) =>
                setSearchData({ ...searchData, checkIn: e.target.value })
              }
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Check-out
            </label>
            <input
              type="time"
              className="mt-1 text-lg font-semibold border-none outline-none"
              value={searchData.checkOut}
              onChange={(e) =>
                setSearchData({ ...searchData, checkOut: e.target.value })
              }
            />
          </div>
        </div>

        <button
          onClick={() => onSearch && onSearch(searchData)}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Search
        </button>
      </div>
    </div>
  );
};

// Service Type Tabs Component
const ServiceTabs = ({ activeTab, onTabChange }: ServiceTabsProps) => {
  const tabs = [
    { id: "hourly", label: "Hourly Stays", active: true },
    { id: "nap", label: "Nap Capsules" },
    { id: "workspace", label: "Workspaces" },
    { id: "showers", label: "Showers" },
    { id: "kids", label: "Kids Area" },
  ];

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`px-6 py-3 rounded-full font-medium transition-colors ${
            activeTab === tab.id || tab.active
              ? "bg-blue-100 text-blue-700 border border-blue-200"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

// Filters Sidebar Component
const FiltersSidebar = ({
  filters: _filters,
  onFilterChange: _onFilterChange,
}: FiltersSidebarProps) => {
  const [priceRange, setPriceRange] = useState([0, 2000]);

  const filterOptions = {
    duration: [
      { id: "1-3hrs", label: "1-3 hrs", checked: true },
      { id: "3-6hrs", label: "3-6 hrs", checked: true },
      { id: "6+hrs", label: "6+ hrs" },
    ],
    closestTo: [
      { id: "boarding", label: "Boarding gate", icon: Plane },
      { id: "security", label: "Security", icon: Shield },
      { id: "checkin", label: "Check-in Counters", icon: CreditCard },
      { id: "baggage", label: "Baggage Claim", icon: Luggage },
    ],
  };

  return (
    <div className="w-80 bg-white p-6 rounded-lg shadow-sm border h-fit">
      <h3 className="text-lg font-semibold mb-6">Filters</h3>

      {/* Price Range */}
      <div className="mb-6">
        <h4 className="font-medium mb-4">Price Range</h4>
        <div className="flex gap-2 mb-3">
          <input
            type="text"
            placeholder="Min"
            className="w-20 px-3 py-2 border rounded text-sm"
            value={`$${priceRange[0]}`}
            readOnly
          />
          <input
            type="text"
            placeholder="Max"
            className="w-20 px-3 py-2 border rounded text-sm"
            value={`$${priceRange[1]}+`}
            readOnly
          />
        </div>
        <input
          type="range"
          min="0"
          max="2000"
          value={priceRange[1]}
          onChange={(e) =>
            setPriceRange([priceRange[0], parseInt(e.target.value)])
          }
          className="w-full"
        />
      </div>

      {/* Duration */}
      <div className="mb-6">
        <h4 className="font-medium mb-4">Duration</h4>
        {filterOptions.duration.map((option) => (
          <label key={option.id} className="flex items-center gap-3 mb-2">
            <input
              type="checkbox"
              defaultChecked={option.checked}
              className="w-4 h-4 text-blue-600 rounded"
            />
            <span className="text-sm">{option.label}</span>
          </label>
        ))}
      </div>

      {/* Closest To */}
      <div>
        <h4 className="font-medium mb-4">Closest To</h4>
        {filterOptions.closestTo.map((option) => (
          <label key={option.id} className="flex items-center gap-3 mb-2">
            <input type="checkbox" className="w-4 h-4 text-blue-600 rounded" />
            <option.icon className="w-4 h-4 text-gray-500" />
            <span className="text-sm">{option.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

// Hotel Card Component
const HotelCard = ({ hotel }: HotelCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
      <div className="flex gap-6">
        {/* Images */}
        <div className="flex-shrink-0">
          <div className="grid grid-cols-2 gap-2 w-48">
            <img
              src={hotel.logoUrl}
              alt={hotel.name}
              className="w-full h-24 object-cover rounded-lg col-span-2"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-xl font-semibold mb-2">{hotel.name}</h3>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="w-4 h-4" />
                <span className="text-sm">{hotel.address.addressLine1}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">${hotel.basePrice}/hr</div>
              {/*<div className="text-sm text-gray-500">${hotel.taxes} taxes and fees</div>*/}
              {/*<div className="text-lg font-semibold mt-1">Total : ${hotel.total}/hr</div>*/}
            </div>
          </div>

          <div className="flex justify-end">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Search Page Component
const SearchPage = () => {
  const [activeTab, setActiveTab] = useState("hourly");
  const [filters, setFilters] = useState({});

  const [searchParams] = useSearchParams();
  const airportLocation = searchParams.get("location");
  const checkIn = searchParams.get("checkIn");
  const checkOut = searchParams.get("checkOut");

  // Mock data
  const hotels = [
    {
      id: 1,
      name: "Happy Stays",
      location: "T3, Near Gate 15",
      price: 49,
      taxes: 2,
      total: 51,
      mainImage: "/api/placeholder/240/120",
      image2: "/api/placeholder/120/80",
      image3: "/api/placeholder/120/80",
    },
    {
      id: 2,
      name: "Cozy Retreats",
      location: "B2, Across from Terminal 3",
      price: 65,
      taxes: 3,
      total: 68,
      mainImage: "/api/placeholder/240/120",
      image2: "/api/placeholder/120/80",
      image3: "/api/placeholder/120/80",
    },
    {
      id: 3,
      name: "Tranquil Escapes",
      location: "A1, Next to Food Court",
      price: 75,
      taxes: 4,
      total: 79,
      mainImage: "/api/placeholder/240/120",
      image2: "/api/placeholder/120/80",
      image3: "/api/placeholder/120/80",
    },
  ];

  const handleSearch = (_searchData) => {};

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <SearchHeader
          location={airportLocation}
          checkIn={checkIn}
          checkOut={checkOut}
          onSearch={handleSearch}
        />

        <ServiceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <div className="flex gap-6">
          <FiltersSidebar filters={filters} onFilterChange={setFilters} />

          <div className="flex-1 space-y-4">
            {hotels.map((hotel) => (
              <HotelCard key={hotel.id} hotel={hotel} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
