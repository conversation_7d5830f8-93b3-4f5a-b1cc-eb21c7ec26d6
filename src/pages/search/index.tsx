import { useState } from "react";
import { useSearchParams } from "react-router";
import SearchHeader from "./search-header";
import ServiceTabs from "./service-tabs";
import FiltersSidebar from "./filters-sidebar";
import HotelCard from "./hotel-card";
import { type IProperty, PremisesTypeEnum } from "./hotel-card/types";

// Main Search Page Component
const SearchPage = () => {
  const [activeTab, setActiveTab] = useState("hourly");
  const [filters, setFilters] = useState({});

  const [searchParams] = useSearchParams();
  const airportLocation = searchParams.get("location");
  const checkIn = searchParams.get("checkIn");
  const checkOut = searchParams.get("checkOut");

  // Mock data
  const hotels: IProperty[] = [
    {
      serviceType: { _id: "1", name: "Hourly Stays", value: "hourly" },
      name: "Happy Stays",
      description: "Comfortable hourly stays near boarding gates",
      premisesType: PremisesTypeEnum.AIRSIDE,
      code: "HS001",
      website: "https://happystays.com",
      address: {
        addressLine1: "T3, Near Gate 15",
        addressLine2: "",
        city: "Bengaluru",
        state: "Karnataka",
        country: "India",
        pincode: "560300",
      },
      buildYear: new Date("2020-01-01"),
      acceptBookingFrom: new Date("2021-01-01"),
      logoUrl: "https://images.10cyrilc.in/images/FILE_1731939889054.jpg",
      basePrice: 49,
    },
    {
      serviceType: { _id: "1", name: "Hourly Stays", value: "hourly" },
      name: "Cozy Retreats",
      description: "Relaxing retreat spaces for travelers",
      premisesType: PremisesTypeEnum.LANDSIDE,
      code: "CR002",
      website: "https://cozyretreats.com",
      address: {
        addressLine1: "B2, Across from Terminal 3",
        addressLine2: "",
        city: "Bengaluru",
        state: "Karnataka",
        country: "India",
        pincode: "560300",
      },
      buildYear: new Date("2019-01-01"),
      acceptBookingFrom: new Date("2020-01-01"),
      logoUrl: "https://images.10cyrilc.in/images/FILE_1731939889054.jpg",
      basePrice: 65,
    },
    {
      serviceType: { _id: "1", name: "Hourly Stays", value: "hourly" },
      name: "Tranquil Escapes",
      description: "Peaceful spaces for rest and relaxation",
      premisesType: PremisesTypeEnum.AIRSIDE,
      code: "TE003",
      website: "https://tranquilescapes.com",
      address: {
        addressLine1: "A1, Next to Food Court",
        addressLine2: "",
        city: "Bengaluru",
        state: "Karnataka",
        country: "India",
        pincode: "560300",
      },
      buildYear: new Date("2021-01-01"),
      acceptBookingFrom: new Date("2022-01-01"),
      logoUrl: "https://images.10cyrilc.in/images/FILE_1731939889054.jpg",
      basePrice: 75,
    },
  ];

  const handleSearch = (_searchData) => {};

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <SearchHeader
          location={airportLocation}
          checkIn={checkIn}
          checkOut={checkOut}
          onSearch={handleSearch}
        />



        <div className="flex flex-1 gap-6">
          <FiltersSidebar filters={filters} onFilterChange={setFilters} />

          <div className="flex-2 space-y-4 w-100">
            <ServiceTabs activeTab={activeTab} onTabChange={setActiveTab} />
            {hotels.map((hotel) => (
              <HotelCard key={hotel.code} hotel={hotel} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
