import React, {useState} from "react";
import {CreditCard, Luggage, Plane, Shield} from "lucide-react";
import type {FiltersSidebarProps} from "./types";

const FiltersSidebar = ({
                            filters: _filters,
                            onFilterChange: _onFilterChange,
                        }: FiltersSidebarProps) => {
    const [priceRange, setPriceRange] = useState([0, 2000]);

    const filterOptions = {
        duration: [
            {id: "1-3hrs", label: "1-3 hrs", checked: true},
            {id: "3-6hrs", label: "3-6 hrs", checked: true},
            {id: "6+hrs", label: "6+ hrs"},
        ],
        closestTo: [
            {id: "boarding", label: "Boarding gate", icon: Plane},
            {id: "security", label: "Security", icon: Shield},
            {id: "checkin", label: "Check-in Counters", icon: CreditCard},
            {id: "baggage", label: "Baggage Claim", icon: Luggage},
        ],
    };

    return (
        <div className="w-80 bg-white p-6 rounded-lg shadow-sm border h-fit sticky top-6">
            <h3 className="text-lg font-semibold mb-6">Filters</h3>

            {/* Price Range */}
            <div className="mb-6">
                <h4 className="font-medium mb-4">Price Range</h4>
                <div className="flex gap-2 mb-3">
                    <input
                        type="text"
                        placeholder="Min"
                        className="w-20 px-3 py-2 border rounded text-sm"
                        value={`$${priceRange[0]}`}
                        readOnly
                    />
                    <input
                        type="text"
                        placeholder="Max"
                        className="w-20 px-3 py-2 border rounded text-sm"
                        value={`$${priceRange[1]}+`}
                        readOnly
                    />
                </div>
                <input
                    type="range"
                    min="0"
                    max="2000"
                    value={priceRange[1]}
                    onChange={(e) =>
                        setPriceRange([priceRange[0], parseInt(e.target.value)])
                    }
                    className="w-full"
                />
            </div>

            {/* Duration */}
            <div className="mb-6">
                <h4 className="font-medium mb-4">Duration</h4>
                {filterOptions.duration.map((option) => (
                    <label key={option.id} className="flex items-center gap-3 mb-2">
                        <input
                            type="checkbox"
                            defaultChecked={option.checked}
                            className="w-4 h-4 text-blue-600 rounded"
                        />
                        <span className="text-sm">{option.label}</span>
                    </label>
                ))}
            </div>

            {/* Closest To */}
            <div>
                <h4 className="font-medium mb-4">Closest To</h4>
                {filterOptions.closestTo.map((option) => (
                    <label key={option.id} className="flex items-center gap-3 mb-2">
                        <input type="checkbox" className="w-4 h-4 text-blue-600 rounded"/>
                        <option.icon className="w-4 h-4 text-gray-500"/>
                        <span className="text-sm">{option.label}</span>
                    </label>
                ))}
            </div>
        </div>
    );
};

export default FiltersSidebar;
