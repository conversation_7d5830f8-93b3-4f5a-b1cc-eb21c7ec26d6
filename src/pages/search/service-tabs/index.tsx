import type {ServiceTabsProps} from "@/pages/search/service-tabs/types.ts";

const ServiceTabs = ({activeTab, onTabChange}: ServiceTabsProps) => {

    const tabs = [
        {id: "hourly", label: "Hourly Stays"},
        {id: "nap", label: "Nap Capsules"},
        {id: "workspace", label: "Workspaces"},
        {id: "showers", label: "Showers"},
        {id: "kids", label: "Kids Area"},
    ];

    return (
        <div className="flex justify-center items-start ">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 flex w-full">
                {tabs.map((tab, index) => (
                    <>
                        <button
                            key={tab.id}
                            onClick={() => onTabChange(tab.id)}
                            className={`px-6 py-3 rounded-lg cursor-pointer text-sm font-medium w-full transition-all duration-200 whitespace-nowrap ${
                                activeTab === tab.id
                                    ? "bg-white text-gray-900 shadow-sm"
                                    : "bg-gray-100 text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                            }`}
                        >
                            {tab.label}
                        </button>

            {index !== tabs.length - 1 && (
                <div className="bg-gray-300 w-[8px]"/>
            )}
                    </>
                ))}
            </div>
        </div>
    );
};

export default ServiceTabs;
