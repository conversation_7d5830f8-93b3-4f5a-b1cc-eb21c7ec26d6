import React, { useState } from "react";
import { Clock, Plane } from "lucide-react";
import type { SearchHeaderProps } from "./types";

const SearchHeader = ({
  location,
  checkIn,
  checkOut,
  onSearch,
}: SearchHeaderProps) => {
  const [searchData, setSearchData] = useState({
    location: location || "KEMPEGOWDA AIRPORT",
    checkIn: checkIn || "11:00 AM",
    checkOut: checkOut || "04:00 PM",
  });

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2 flex-1 min-w-64">
          <Plane className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Location
            </label>
            <select
              className="mt-1 text-lg font-semibold border-none outline-none bg-transparent"
              value={searchData.location}
              onChange={(e) =>
                setSearchData({ ...searchData, location: e.target.value })
              }
            >
              <option>KEMPEGOWDA AIRPORT</option>
              <option>MUMBAI AIRPORT</option>
              <option>DELHI AIRPORT</option>
            </select>
            <p className="text-sm text-gray-500">Bengaluru, India</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Check-in
            </label>
            <input
              type="time"
              className="mt-1 text-lg font-semibold border-none outline-none"
              value={searchData.checkIn}
              onChange={(e) =>
                setSearchData({ ...searchData, checkIn: e.target.value })
              }
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Check-out
            </label>
            <input
              type="time"
              className="mt-1 text-lg font-semibold border-none outline-none"
              value={searchData.checkOut}
              onChange={(e) =>
                setSearchData({ ...searchData, checkOut: e.target.value })
              }
            />
          </div>
        </div>

        <button
          onClick={() => onSearch && onSearch(searchData)}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Search
        </button>
      </div>
    </div>
  );
};

export default SearchHeader;
