import { useState } from "react";
import { Clock, Plane } from "lucide-react";
import type { SearchHeaderProps } from "./types";

const SearchHeader = ({
  location,
  checkIn,
  checkOut,
  onSearch,
}: SearchHeaderProps) => {
  const [searchData, setSearchData] = useState({
    location: location || "KEMPEGOWDA AIRPORT",
    checkIn: checkIn || "11:00 AM",
    checkOut: checkOut || "04:00 PM",
  });

  return (
    <div className={"flex w-full p-4 rounded-lg mb-6 gap-4"}>
      <div className="bg-white w-full rounded-lg shadow-md p-2 flex items-center justify-between border border-gray-200">
        <div className="flex items-center gap-2 flex-1 pr-4 border-r border-gray-300 pl-4">
          <Plane className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-xs font-medium text-gray-600">
              Location
            </label>
            <select
              className="mt-1 text-lg font-semibold text-black border-none outline-none bg-transparent appearance-none"
              value={searchData.location}
              onChange={(e) =>
                setSearchData({ ...searchData, location: e.target.value })
              }
            >
              <option>KEMPEGOWDA AIRPORT</option>
              <option>MUMBAI AIRPORT</option>
              <option>DELHI AIRPORT</option>
            </select>
            <p className="text-xs text-gray-500">Bengaluru, India</p>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-1 pr-4 border-r border-gray-300  pl-4">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-xs font-medium text-gray-600">
              Check-in
            </label>
            <select
              className="mt-1 text-lg font-semibold text-black border-none outline-none bg-transparent appearance-none"
              value={searchData.checkIn}
              onChange={(e) =>
                setSearchData({ ...searchData, checkIn: e.target.value })
              }
            >
              <option>11:00 AM</option>
              <option>12:00 PM</option>
              <option>01:00 PM</option>
            </select>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-1  pl-4">
          <Clock className="w-5 h-5 text-gray-500" />
          <div>
            <label className="block text-xs font-medium text-gray-600">
              Check-out
            </label>
            <select
              className="mt-1 text-lg font-semibold text-black border-none outline-none bg-transparent appearance-none"
              value={searchData.checkOut}
              onChange={(e) =>
                setSearchData({ ...searchData, checkOut: e.target.value })
              }
            >
              <option>04:00 PM</option>
              <option>05:00 PM</option>
              <option>06:00 PM</option>
            </select>
          </div>
        </div>
      </div>
      <button
        onClick={() => onSearch && onSearch(searchData)}
        className="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors"
      >
        Search
      </button>
    </div>
  );
};

export default SearchHeader;
