import {MapPin} from "lucide-react";
import type {HotelCardProps} from "./types";

const HotelCard = ({hotel}: HotelCardProps) => {
    return (
        <div className="bg-gray-50">
            <div className="max-w-5xl mx-auto">
                <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                    <div className="flex">
                        {/* Single Image */}
                        <div className="w-72 p-4">
                            <img
                                src={hotel.logoUrl}
                                alt="Hotel room"
                                className="w-full h-48 object-cover rounded-lg"
                            />
                        </div>

                        {/* Content */}
                        <div className="flex-1 p-6 flex flex-col justify-between">
                            <div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-3">{hotel.name}</h3>
                                <div className="flex items-center gap-2 text-gray-600">
                                    <MapPin className="w-4 h-4" />
                                    <span className="text-sm">{hotel.address.addressLine1}</span>
                                </div>
                            </div>

                            {/* Pricing and Button */}
                            <div className="self-end text-right">
                                <div className="text-2xl font-bold text-gray-900 mb-1">${hotel.basePrice}/hr</div>
                                <button className="bg-blue-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                    Book Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HotelCard;
