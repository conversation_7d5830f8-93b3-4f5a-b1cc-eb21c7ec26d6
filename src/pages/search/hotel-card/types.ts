export enum PremisesTypeEnum {
  AIRSIDE = "Airside",
  LANDSIDE = "Landside",
}

export type IProperty = {
  serviceType: IServiceType;
  name: string;
  description: string;
  premisesType: PremisesTypeEnum;
  code: string;
  website: string;
  address: IAddress;
  buildYear: Date;
  acceptBookingFrom: Date;
  logoUrl: string;
  basePrice: number;
};

export type IServiceType = {
  _id: string;
  name: string;
  value: string;
};

export type IAddress = {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
};

export type HotelCardProps = {
  hotel: IProperty;
};
