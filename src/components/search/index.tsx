import { useState } from "react";
import { ChevronDown, Clock, MapPin } from "lucide-react";

const locations: string[] = [
  "New York, NY",
  "Los Angeles, CA",
  "Chicago, IL",
  "Miami, FL",
  "Las Vegas, NV",
  "San Francisco, CA",
  "Boston, MA",
  "Seattle, WA",
];

type DropdownType = "location" | null;

const Search = () => {
  const [location, setLocation] = useState<string>("");
  const [checkIn, setCheckIn] = useState<string>("");
  const [checkOut, setCheckOut] = useState<string>("");
  const [activeDropdown, setActiveDropdown] = useState<DropdownType>(null);

  const toggleDropdown = (dropdown: DropdownType) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const selectLocation = (loc: string) => {
    setLocation(loc);
    setActiveDropdown(null);
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="flex items-center justify-center p-4">
      <div className="max-w-6xl w-full">
        <div className="backdrop-blur-xs border border-white rounded-2xl p-6">
          <h1 className="capitalize text-white font-medium text-sm mb-2">
            Tell us how much time you have
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border border-white/30 rounded-xl overflow-hidden">
            {/* Location Dropdown */}
            <div className="relative">
              <button
                onClick={() => toggleDropdown("location")}
                className="w-full px-4 py-4 text-left text-white flex items-center justify-between"
                aria-expanded={activeDropdown === "location"}
              >
                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-white/80" />
                  <div>
                    <div className="text-sm text-white/80">Location</div>
                    <div className="text-white">
                      {location || "Where are you going?"}
                    </div>
                  </div>
                </div>
                <ChevronDown
                  className={`w-5 h-5 text-white/80 transform transition-transform ${
                    activeDropdown === "location" ? "rotate-180" : ""
                  }`}
                />
              </button>

              {activeDropdown === "location" && (
                <div className="absolute z-[999] top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl max-h-60 overflow-y-auto">
                  {locations.map((loc, index) => (
                    <button
                      key={index}
                      onClick={() => selectLocation(loc)}
                      className="w-full px-4 py-3 text-left hover:bg-gray-100 transition-colors border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-center space-x-3">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span>{loc}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Check-in Date */}
            <div className="relative border-l border-white/20">
              <div className="w-full px-4 py-4 text-white">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-white/80" />
                  <div className="flex-1">
                    <label className="text-sm text-white/80 block">
                      Check-in
                    </label>
                    <input
                      type="date"
                      value={checkIn}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setCheckIn(e.target.value)
                      }
                      className="bg-transparent text-white placeholder-white/60 border-none outline-none w-full text-base"
                      min={new Date().toISOString().split("T")[0]}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Check-out Date */}
            <div className="relative border-l border-white/20">
              <div className="w-full px-4 py-4 text-white">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-white/80" />
                  <div className="flex-1">
                    <label className="text-sm text-white/80 block">
                      Check-out
                    </label>
                    <input
                      type="date"
                      value={checkOut}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setCheckOut(e.target.value)
                      }
                      className="bg-transparent text-white placeholder-white/60 border-none outline-none w-full text-base"
                      min={checkIn || new Date().toISOString().split("T")[0]}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search Button */}
        <div className="text-center mt-[-10px] relative z-20">
          <button className="btn-primary text-white font-medium px-12 py-4 rounded-xl text-lg transition-all duration-200 hover:scale-105 shadow-xl">
            Explore Services
          </button>
        </div>

        {/* Selected Info Display */}
        {(location || checkIn || checkOut) && (
          <div className="mt-8 bg-white/10 backdrop-blur-sm rounded-xl p-4 text-white text-center">
            <div className="text-sm text-white/80 mb-2">Your Selection:</div>
            <div className="space-y-1">
              {location && (
                <div>
                  <span className="text-white/80">Location:</span> {location}
                </div>
              )}
              {checkIn && (
                <div>
                  <span className="text-white/80">Check-in:</span>{" "}
                  {formatDate(checkIn)}
                </div>
              )}
              {checkOut && (
                <div>
                  <span className="text-white/80">Check-out:</span>{" "}
                  {formatDate(checkOut)}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Dropdown Dismiss Overlay */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setActiveDropdown(null)}
        ></div>
      )}
    </div>
  );
};

export default Search;
