import { Instagram, Linkedin } from "lucide-react";
import logo from "@/assets/logo.png";
import {
  bottomData,
  companyData,
  exploreData,
  resourcesData,
  serviceData,
} from "@/components/footer/data.ts";
import { Link } from "react-router";
const XIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-gray-300 py-12 px-6">
      <div className="max-w-6xl mx-auto">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Logo and Social Links */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <img src={logo} alt="Logo" className="w-20 mr-4" />
            </div>

            {/* Social Media Icons */}
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Linkedin size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Instagram size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <XIcon />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-white font-semibold mb-4">Services</h3>
            <ul className="space-y-3">
              {serviceData.map((data) => (
                <li>
                  <Link
                    to={data.link}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {data.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Explore Column */}
          <div>
            <h3 className="text-white font-semibold mb-4">Explore</h3>
            <ul className="space-y-3">
              {exploreData.map((data) => (
                <li>
                  <Link
                    to={data.link}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {data.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources and Company Columns Combined */}
          <div className="space-y-8">
            {/* Resources */}
            <div>
              <h3 className="text-white font-semibold mb-4">Resources</h3>
              <ul className="space-y-3">
                {resourcesData.map((data) => (
                  <li>
                    <Link
                      to={data.link}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      {data.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-white font-semibold mb-4">Company</h3>
              <ul className="space-y-3">
                {companyData.map((data) => (
                  <li>
                    <Link
                      to={data.link}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      {data.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
          {/* Legal Links */}
          <div className="flex flex-wrap gap-6 mb-4 md:mb-0">
            {bottomData.map((data) => (
              <a
                href={data.link}
                className="text-gray-400 hover:text-white transition-colors text-sm"
              >
                {data.name}
              </a>
            ))}
          </div>

          {/* Copyright */}
          <div className="text-gray-400 text-sm">
            Copyright line: © {new Date().getFullYear()} StayTransit. All
            rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
