import { createBrowserRouter } from "react-router";
import Layout from "@/layout";
import Home from "@/pages/home";
import SearchPage from "@/pages/search";

const routes = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        index: true,
        element: <SearchPage />,
      },
    ],
  },
]);

export default routes;
